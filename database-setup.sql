-- Database setup for NextAuth.js with SQL Server
-- Run this script in your SQL Server Management Studio or Azure Data Studio

-- Check if users table exists, if not create it
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        FULLNAME NVARCHAR(255) NOT NULL,
        EMAIL NVARCHAR(255) UNIQUE NOT NULL,
        PASSWORD NVARCHAR(255) NOT NULL,
        CREATED_AT DATETIME2 DEFAULT GETDATE(),
        UPDATED_AT DATETIME2 DEFAULT GETDATE()
    );
    PRINT 'Users table created successfully';
END
ELSE
BEGIN
    -- If table exists, check and modify columns if needed
    PRINT 'Users table already exists. Checking column sizes...';
    
    -- Check if PASSWORD column is too small and alter it
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'PASSWORD' 
        AND CHARACTER_MAXIMUM_LENGTH < 255
    )
    BEGIN
        ALTER TABLE users ALTER COLUMN PASSWORD NVARCHAR(255) NOT NULL;
        PRINT 'PASSWORD column size increased to 255 characters';
    END
    
    -- Check if EMAIL column is too small and alter it
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'EMAIL' 
        AND CHARACTER_MAXIMUM_LENGTH < 255
    )
    BEGIN
        ALTER TABLE users ALTER COLUMN EMAIL NVARCHAR(255) NOT NULL;
        PRINT 'EMAIL column size increased to 255 characters';
    END
    
    -- Check if FULLNAME column is too small and alter it
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'FULLNAME' 
        AND CHARACTER_MAXIMUM_LENGTH < 255
    )
    BEGIN
        ALTER TABLE users ALTER COLUMN FULLNAME NVARCHAR(255) NOT NULL;
        PRINT 'FULLNAME column size increased to 255 characters';
    END
END

-- Create index on EMAIL for faster lookups
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_users_EMAIL')
BEGIN
    CREATE INDEX IX_users_EMAIL ON users(EMAIL);
    PRINT 'Index on EMAIL column created';
END

-- Display current table structure
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;

-- Create production_orders table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='production_orders' AND xtype='U')
BEGIN
    CREATE TABLE production_orders (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        PROCESS NVARCHAR(100) NOT NULL,
        LINE NVARCHAR(50) NOT NULL,
        PRODUCTION_ORDER NVARCHAR(100) UNIQUE NOT NULL,
        STATUS NVARCHAR(20) NOT NULL DEFAULT 'PENDING',
        START_DATE DATETIME2 NULL,
        END_DATE DATETIME2 NULL,
        USER_ID INT NULL,
        CREATED_AT DATETIME2 DEFAULT GETDATE(),
        UPDATED_AT DATETIME2 DEFAULT GETDATE(),
        UPDATED_BY INT NULL
    );
    PRINT 'Production orders table created successfully';
END
ELSE
BEGIN
    PRINT 'Production orders table already exists';
END

-- Create index on PRODUCTION_ORDER for faster lookups
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_production_orders_PRODUCTION_ORDER')
BEGIN
    CREATE INDEX IX_production_orders_PRODUCTION_ORDER ON production_orders(PRODUCTION_ORDER);
    PRINT 'Index on PRODUCTION_ORDER column created';
END

-- Create index on STATUS for faster filtering
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_production_orders_STATUS')
BEGIN
    CREATE INDEX IX_production_orders_STATUS ON production_orders(STATUS);
    PRINT 'Index on STATUS column created';
END

-- Display production_orders table structure
SELECT
    'production_orders' AS TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'production_orders'
ORDER BY ORDINAL_POSITION;

PRINT 'Database setup completed successfully!';
