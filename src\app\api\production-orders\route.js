import { getConnection } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const { PROCESS, LINE, PRODUCTION_ORDER, STATUS, USER_ID } = await request.json()

    // Validation
    if (!PROCESS || !LINE || !PRODUCTION_ORDER) {
      return NextResponse.json(
        { message: 'PROCESS, LINE, and PRODUCTION_ORDER are required' },
        { status: 400 }
      )
    }

    const pool = await getConnection()

    // Check if production order already exists
    const existingPO = await pool.request()
      .input('PRODUCTION_ORDER', PRODUCTION_ORDER)
      .query('SELECT USER_ID FROM SFTRACKINGDATA WHERE PRODUCTION_ORDER = @PRODUCTION_ORDER')

    if (existingPO.recordset.length > 0) {
      return NextResponse.json(
        { message: 'Production Order already exists' },
        { status: 400 }
      )
    }

    // Insert new production order (no ID needed if it's auto-increment)
    await pool.request()
      .input('PROCESS', PROCESS)
      .input('LINE', LINE)
      .input('PRODUCTION_ORDER', PRODUCTION_ORDER)
      .input('USER_ID', USER_ID)
      .input('EMP_ID', USER_ID) // Using USER_ID for EMP_ID as well
      .query(`
        INSERT INTO SFTRACKINGDATA
        (PROCESS, LINE, PRODUCTION_ORDER, USER_ID, EMP_ID)
        VALUES
        (@PROCESS, @LINE, @PRODUCTION_ORDER, @USER_ID, @EMP_ID)
      `)

    return NextResponse.json(
      {
        message: 'Production Order saved successfully',
        production_order: PRODUCTION_ORDER
      },
      { status: 201 }
    )

  } catch (error) {
    console.error('Production Order save error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const userId = searchParams.get('user_id')

    const pool = await getConnection()
    let query = 'SELECT * FROM SFTRACKINGDATA WHERE 1=1'
    const inputs = []

    if (userId) {
      query += ' AND USER_ID = @userId'
      inputs.push({ name: 'userId', value: userId })
    }

    query += ' ORDER BY PRODUCTION_ORDER DESC'

    const request_obj = pool.request()
    inputs.forEach(input => {
      request_obj.input(input.name, input.value)
    })

    const result = await request_obj.query(query)

    return NextResponse.json(
      { production_orders: result.recordset },
      { status: 200 }
    )

  } catch (error) {
    console.error('Production Orders fetch error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
