"use client"

import { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'

export default function StartPOPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const scannerInputRef = useRef(null)

  const [formData, setFormData] = useState({
    PROCESS: '',
    LINE: '',
    PRODUCTION_ORDER: '',
    STATUS: 'PENDING'
  })
  
  const [isScanning, setIsScanning] = useState(false)
  const [message, setMessage] = useState('')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    if (!session) {
      router.push('/login')
    }
  }, [session, status, router])

  useEffect(() => {
    // Focus on scanner input when component mounts
    if (scannerInputRef.current) {
      scannerInputRef.current.focus()
    }
  }, [])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleScannerInput = async (e) => {
    if (e.key === 'Enter' && e.target.value.trim()) {
      const scannedValue = e.target.value.trim()
      
      // Auto-fill production order from scanner
      setFormData(prev => ({
        ...prev,
        PRODUCTION_ORDER: scannedValue
      }))

      // Auto-save after scanning
      await autoSaveRecord(scannedValue)
      
      // Clear scanner input
      e.target.value = ''
    }
  }

  const autoSaveRecord = async (productionOrder) => {
    if (!formData.PROCESS || !formData.LINE) {
      setMessage('Please fill in PROCESS and LINE before scanning')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/production-orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          PROCESS: formData.PROCESS,
          LINE: formData.LINE,
          PRODUCTION_ORDER: productionOrder,
          STATUS: 'SCANNED',
          USER_ID: session.user.id
        })
      })

      const data = await response.json()

      if (response.ok) {
        setMessage(`✅ Production Order ${productionOrder} saved successfully!`)
        // Reset form for next scan
        setFormData(prev => ({
          ...prev,
          PRODUCTION_ORDER: '',
          STATUS: 'PENDING'
        }))
      } else {
        setMessage(`❌ Error: ${data.message}`)
      }
    } catch (error) {
      setMessage('❌ Error saving production order')
      console.error('Save error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleConfirmStart = async () => {
    if (!formData.PRODUCTION_ORDER) {
      setMessage('Please scan a production order first')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/production-orders/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          PRODUCTION_ORDER: formData.PRODUCTION_ORDER,
          USER_ID: session.user.id
        })
      })

      const data = await response.json()

      if (response.ok) {
        setMessage(`✅ Production Order ${formData.PRODUCTION_ORDER} started successfully!`)
        // Reset form
        setFormData({
          PROCESS: '',
          LINE: '',
          PRODUCTION_ORDER: '',
          STATUS: 'PENDING'
        })
        // Refocus scanner
        if (scannerInputRef.current) {
          scannerInputRef.current.focus()
        }
      } else {
        setMessage(`❌ Error: ${data.message}`)
      }
    } catch (error) {
      setMessage('❌ Error starting production order')
      console.error('Start error:', error)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Navbar session={session} />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">
              START PO FORM
            </h1>

            {message && (
              <div className={`p-4 rounded-md mb-6 ${
                message.includes('✅') 
                  ? 'bg-green-100 border border-green-400 text-green-700'
                  : 'bg-red-100 border border-red-400 text-red-700'
              }`}>
                {message}
              </div>
            )}

            <form className="space-y-6">
              {/* Process Field */}
              <div>
                <label htmlFor="PROCESS" className="block text-sm font-medium text-gray-700 mb-2">
                  PROCESS *
                </label>
                <input
                  type="text"
                  id="PROCESS"
                  name="PROCESS"
                  value={formData.PROCESS}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter process name"
                />
              </div>

              {/* Line Field */}
              <div>
                <label htmlFor="LINE" className="block text-sm font-medium text-gray-700 mb-2">
                  LINE *
                </label>
                <input
                  type="text"
                  id="LINE"
                  name="LINE"
                  value={formData.LINE}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter line number"
                />
              </div>

              {/* Scanner Input */}
              <div>
                <label htmlFor="scanner" className="block text-sm font-medium text-gray-700 mb-2">
                  SCAN PRODUCTION ORDER
                </label>
                <input
                  ref={scannerInputRef}
                  type="text"
                  id="scanner"
                  onKeyDown={handleScannerInput}
                  className="w-full px-3 py-2 border-2 border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-blue-50"
                  placeholder="Scan barcode here (press Enter after scan)"
                  autoComplete="off"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Focus here and scan the production order barcode
                </p>
              </div>

              {/* Production Order Display */}
              <div>
                <label htmlFor="PRODUCTION_ORDER" className="block text-sm font-medium text-gray-700 mb-2">
                  PRODUCTION ORDER
                </label>
                <input
                  type="text"
                  id="PRODUCTION_ORDER"
                  name="PRODUCTION_ORDER"
                  value={formData.PRODUCTION_ORDER}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                  placeholder="Will be filled automatically after scanning"
                  readOnly
                />
              </div>

              {/* Status Display */}
              <div>
                <label htmlFor="STATUS" className="block text-sm font-medium text-gray-700 mb-2">
                  STATUS
                </label>
                <input
                  type="text"
                  id="STATUS"
                  value={formData.STATUS}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                  readOnly
                />
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={handleConfirmStart}
                  disabled={loading || !formData.PRODUCTION_ORDER}
                  className="flex-1 bg-green-600 text-white py-3 px-6 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Processing...' : 'CONFIRM START'}
                </button>
                
                <button
                  type="button"
                  onClick={() => {
                    setFormData({
                      PROCESS: '',
                      LINE: '',
                      PRODUCTION_ORDER: '',
                      STATUS: 'PENDING'
                    })
                    setMessage('')
                    if (scannerInputRef.current) {
                      scannerInputRef.current.focus()
                    }
                  }}
                  className="flex-1 bg-gray-600 text-white py-3 px-6 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  CLEAR FORM
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
