import { getConnection } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const { PRODUCTION_ORDER, USER_ID } = await request.json()

    // Validation
    if (!PRODUCTION_ORDER) {
      return NextResponse.json(
        { message: 'PRODUCTION_ORDER is required' },
        { status: 400 }
      )
    }

    const pool = await getConnection()

    // Check if production order exists
    const existingPO = await pool.request()
      .input('PRODUCTION_ORDER', PRODUCTION_ORDER)
      .query('SELECT * FROM SFTRACKINGDATA WHERE PRODUCTION_ORDER = @PRODUCTION_ORDER')

    if (existingPO.recordset.length === 0) {
      return NextResponse.json(
        { message: 'Production Order not found' },
        { status: 404 }
      )
    }

    const productionOrder = existingPO.recordset[0]

    // Check if already started
    if (productionOrder.START_DATE) {
      return NextResponse.json(
        { message: 'Production Order already started' },
        { status: 400 }
      )
    }

    // Update production order with start date/time
    const startDateTime = new Date()

    await pool.request()
      .input('PRODUCTION_ORDER', PRODUCTION_ORDER)
      .input('START_DATE', startDateTime)
      .input('USER_ID', USER_ID)
      .query(`
        UPDATE SFTRACKINGDATA
        SET
          START_DATE = @START_DATE,
          USER_ID = @USER_ID
        WHERE PRODUCTION_ORDER = @PRODUCTION_ORDER
      `)

    return NextResponse.json(
      {
        message: 'Production Order started successfully',
        production_order: PRODUCTION_ORDER,
        start_date: startDateTime
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Production Order start error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
