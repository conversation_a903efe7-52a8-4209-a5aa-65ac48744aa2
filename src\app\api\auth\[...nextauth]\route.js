import NextAuth from 'next-auth'
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { getConnection } from '@/lib/db'

const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        EMAIL: { label: 'EMAIL', type: 'EMAIL' },
        PASSWORD: { label: 'PASSWORD', type: 'PASSWORD' }
      },
      async authorize(credentials) {
        if (!credentials?.EMAIL || !credentials?.PASSWORD) {
          return null
        }

        try {
          const pool = await getConnection()
          const result = await pool.request()
            .input('EMAIL', credentials.EMAIL)
            .query('SELECT * FROM users WHERE EMAIL = @EMAIL')

          const user = result.recordset[0]

          if (user && await bcrypt.compare(credentials.PASSWORD, user.PASSWORD)) {
            return {
              id: user.ID,
              email: user.EMAIL,
              name: user.FULLNAME || user.EMAIL
            }
          }
          return null
        } catch (error) {
          console.error('Auth error:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  pages: {
    signIn: '/login',
    signUp: '/register'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id
      }
      return session
    }
  }
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }
