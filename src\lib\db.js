import sql from 'mssql';

const config = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER, // e.g. '*************\\mssqlserver'
  port: parseInt(process.env.DB_PORT),
  database: process.env.DB_NAME,
  options: {
    encrypt: false, // Set to true if using Azure or SSL
    trustServerCertificate: true
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  }
};

let pool;

export async function getConnection() {
  // Skip database connection during build
  if (process.env.BUILDING === 'true') {
    throw new Error('Build mode - no DB');
  }

  if (!pool) {
    try {
      pool = await sql.connect(config);
      console.log("✅ Connected to SQL Server");
    } catch (err) {
      console.error('SQL Server Connection Error:', err);
      throw err;
    }
  }
  return pool;
}
