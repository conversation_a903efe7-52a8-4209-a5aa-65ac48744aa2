// import bcrypt from 'bcryptjs' // Temporarily disabled - fix database schema first
import { getConnection } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const { FULLNAME, EMAIL, PASSWORD } = await request.json()

    // Validation
    if (!FULLNAME || !EMAIL || !PASSWORD) {
      return NextResponse.json(
        { message: 'All fields are required' },
        { status: 400 }
      )
    }

    if (PASSWORD.length < 6) {
      return NextResponse.json(
        { message: 'Password must be at least 6 characters long' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const pool = await getConnection()
    const existingUser = await pool.request()
      .input('EMAIL', EMAIL)
      .query('SELECT ID FROM users WHERE EMAIL = @EMAIL')

    if (existingUser.recordset.length > 0) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // TEMPORARY: Store plain text password (NOT SECURE - only for testing)
    // TODO: Fix database schema and use: const hashedPassword = await bcrypt.hash(PASSWORD, 10)
    const hashedPassword = PASSWORD

    // Create user
    await pool.request()
      .input('FULLNAME', FULLNAME)
      .input('EMAIL', EMAIL)
      .input('PASSWORD', hashedPassword)
      .query('INSERT INTO users (FULLNAME, EMAIL, PASSWORD) VALUES (@FULLNAME, @EMAIL, @PASSWORD)')

    return NextResponse.json(
      { message: 'User created successfully' },
      { status: 201 }
    )

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
