import bcrypt from 'bcryptjs'
import { getConnection } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const { FULLNAME, EMAIL, PASSWORD } = await request.json()

    // Validation
    if (!FULLNAME || !EMAIL || !PASSWORD) {
      return NextResponse.json(
        { message: 'All fields are required' },
        { status: 400 }
      )
    }

    if (PASSWORD.length < 6) {
      return NextResponse.json(
        { message: 'Password must be at least 6 characters long' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const pool = await getConnection()
    const existingUser = await pool.request()
      .input('EMAIL', EMAIL)
      .query('SELECT ID FROM users WHERE EMAIL = @EMAIL')

    if (existingUser.recordset.length > 0) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password securely
    const hashedPassword = await bcrypt.hash(PASSWORD, 10)

    // Get the next available ID (temporary fix - should use IDENTITY column instead)
    const maxIdResult = await pool.request()
      .query('SELECT ISNULL(MAX(ID), 0) + 1 AS NextID FROM users')
    const nextId = maxIdResult.recordset[0].NextID

    // Create user with manual ID
    await pool.request()
      .input('ID', nextId)
      .input('FULLNAME', FULLNAME)
      .input('EMAIL', EMAIL)
      .input('PASSWORD', hashedPassword)
      .query('INSERT INTO users (ID, FULLNAME, EMAIL, PASSWORD) VALUES (@ID, @FULLNAME, @EMAIL, @PASSWORD)')

    return NextResponse.json(
      { message: 'User created successfully' },
      { status: 201 }
    )

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
