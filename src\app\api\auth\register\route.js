import bcrypt from 'bcryptjs'
import { getConnection } from '@/app/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const { name, email, password } = await request.json()

    // Validation
    if (!name || !email || !password) {
      return NextResponse.json(
        { message: 'All fields are required' },
        { status: 400 }
      )
    }

    if (password.length < 6) {
      return NextResponse.json(
        { message: 'Password must be at least 6 characters long' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const pool = await getConnection()
    const existingUser = await pool.request()
      .input('email', email)
      .query('SELECT id FROM users WHERE email = @email')

    if (existingUser.recordset.length > 0) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    await pool.request()
      .input('name', name)
      .input('email', email)
      .input('password', hashedPassword)
      .query('INSERT INTO users (name, email, password) VALUES (@name, @email, @password)')

    return NextResponse.json(
      { message: 'User created successfully' },
      { status: 201 }
    )

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
